{"start_time": "2025-07-06T10:04:09.397572", "target": "http://localhost:8000", "config_file": "config.yml", "scans": {"static_analysis": {"status": "completed", "tools": [], "errors": ["Semgrep not available", "Bandit not available", "Safety not available", "pip-audit not available"]}, "dependency_scan": {"status": "completed", "tools": [], "errors": ["Trivy not available"]}, "dast": {"status": "completed", "tools": [], "errors": ["OWASP ZAP not available"], "target": "http://localhost:8000"}, "agents": null}, "end_time": "2025-07-06T10:05:55.626562", "duration": "0:01:46.228990", "summary": {"scan_timestamp": "2025-07-06T10:05:55.626582", "results_directory": "results_20250706_100409", "tools_executed": [], "vulnerabilities_found": 0, "critical_issues": 0, "high_issues": 0, "medium_issues": 0, "low_issues": 0, "files_analyzed": []}}