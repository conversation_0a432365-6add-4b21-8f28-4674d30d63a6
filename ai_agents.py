#!/usr/bin/env python3
"""
security_mvp/ai_agents.py

AI Security Agents using CrewAI and OpenRouter
Defines specialized security testing agents with AI capabilities.
"""
import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from crewai import Agent, Task, Crew, Process
from rich.console import Console
from rich.panel import Panel

from ai_config import get_ai_config, AIConfigManager

logger = logging.getLogger(__name__)
console = Console()

# Simple tool functions instead of BaseTool classes
def security_analysis_tool(scan_results: str, scan_type: str = "general") -> str:
    """Analyzes security scan results and identifies vulnerabilities."""
    try:
        # Parse scan results if JSON
        if scan_results.strip().startswith('{'):
            results = json.loads(scan_results)
            return _analyze_json_results(results, scan_type)
        else:
            return _analyze_text_results(scan_results, scan_type)
    except Exception as e:
        return f"Error analyzing scan results: {str(e)}"

def _analyze_json_results(results: dict, scan_type: str) -> str:
    """Analyze JSON formatted scan results."""
    analysis = f"Security Analysis Report - {scan_type.upper()}\n"
    analysis += "=" * 50 + "\n\n"

    if 'vulnerabilities' in results:
        vulns = results['vulnerabilities']
        analysis += f"Found {len(vulns)} vulnerabilities:\n"
        for vuln in vulns[:10]:  # Limit to first 10
            severity = vuln.get('severity', 'Unknown')
            title = vuln.get('title', 'Unknown vulnerability')
            analysis += f"- [{severity}] {title}\n"

    if 'summary' in results:
        analysis += f"\nSummary: {results['summary']}\n"

    return analysis

def _analyze_text_results(results: str, scan_type: str) -> str:
    """Analyze text formatted scan results."""
    lines = results.split('\n')
    analysis = f"Security Analysis Report - {scan_type.upper()}\n"
    analysis += "=" * 50 + "\n\n"
    analysis += f"Analyzed {len(lines)} lines of scan output\n"
    analysis += f"Key findings from {scan_type} scan:\n"

    # Look for common vulnerability indicators
    indicators = ['error', 'warning', 'critical', 'high', 'medium', 'low', 'vulnerability']
    findings = []
    for line in lines:
        if any(indicator in line.lower() for indicator in indicators):
            findings.append(line.strip())

    for finding in findings[:10]:  # Limit to first 10
        analysis += f"- {finding}\n"

    return analysis

def vulnerability_exploit_tool(vulnerability: str, target_info: str = "") -> str:
    """Generates safe exploit suggestions for identified vulnerabilities."""
    exploit_suggestions = f"Exploit Analysis for: {vulnerability}\n"
    exploit_suggestions += "=" * 50 + "\n\n"

    # Common vulnerability patterns and their exploit suggestions
    vuln_patterns = {
        'sql injection': [
            "Test with single quotes to confirm SQL injection",
            "Use UNION SELECT to extract data",
            "Try time-based blind SQL injection techniques",
            "Consider using sqlmap for automated testing"
        ],
        'xss': [
            "Test with <script>alert('XSS')</script>",
            "Try DOM-based XSS payloads",
            "Test for stored XSS in user inputs",
            "Use XSS Hunter for blind XSS detection"
        ],
        'csrf': [
            "Create a malicious form that submits to the target",
            "Test if anti-CSRF tokens are properly validated",
            "Check for SameSite cookie attributes",
            "Verify referer header validation"
        ],
        'directory traversal': [
            "Test with ../../../etc/passwd",
            "Try URL encoding: %2e%2e%2f",
            "Test with null bytes: ..%00/",
            "Use double encoding techniques"
        ]
    }

    vuln_lower = vulnerability.lower()
    for pattern, suggestions in vuln_patterns.items():
        if pattern in vuln_lower:
            exploit_suggestions += f"Suggested exploit techniques:\n"
            for i, suggestion in enumerate(suggestions, 1):
                exploit_suggestions += f"{i}. {suggestion}\n"
            break
    else:
        exploit_suggestions += "Generic exploit testing recommendations:\n"
        exploit_suggestions += "1. Analyze the vulnerability context\n"
        exploit_suggestions += "2. Research known exploits for this vulnerability type\n"
        exploit_suggestions += "3. Test in a controlled environment first\n"
        exploit_suggestions += "4. Document all findings and steps\n"

    exploit_suggestions += f"\nTarget Information: {target_info}\n"
    exploit_suggestions += "\n⚠️  WARNING: Only test on authorized systems!"

    return exploit_suggestions

def security_report_tool(findings: str, target: str, scan_types: str = "") -> str:
    """Generates comprehensive security assessment reports."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    report = f"""
SECURITY ASSESSMENT REPORT
==========================

Target: {target}
Date: {timestamp}
Scan Types: {scan_types}

EXECUTIVE SUMMARY
-----------------
{_generate_executive_summary(findings)}

DETAILED FINDINGS
-----------------
{findings}

RISK ASSESSMENT
---------------
{_generate_risk_assessment(findings)}

RECOMMENDATIONS
---------------
{_generate_recommendations(findings)}

NEXT STEPS
----------
1. Prioritize critical and high-severity vulnerabilities
2. Implement recommended security controls
3. Conduct follow-up testing after remediation
4. Schedule regular security assessments

Report generated by Security MVP AI Agent System
"""
    return report

def _generate_executive_summary(findings: str) -> str:
    """Generate executive summary based on findings."""
    if 'critical' in findings.lower():
        return "Critical security vulnerabilities identified requiring immediate attention."
    elif 'high' in findings.lower():
        return "High-severity security issues found that should be addressed promptly."
    elif 'medium' in findings.lower():
        return "Medium-severity security concerns identified for remediation."
    else:
        return "Security assessment completed with findings requiring review."

def _generate_risk_assessment(findings: str) -> str:
    """Generate risk assessment based on findings."""
    risk_levels = []
    if 'critical' in findings.lower():
        risk_levels.append("CRITICAL: Immediate risk of system compromise")
    if 'high' in findings.lower():
        risk_levels.append("HIGH: Significant security risk")
    if 'medium' in findings.lower():
        risk_levels.append("MEDIUM: Moderate security concerns")
    if 'low' in findings.lower():
        risk_levels.append("LOW: Minor security issues")

    return '\n'.join(risk_levels) if risk_levels else "Risk assessment pending detailed analysis."

def _generate_recommendations(findings: str) -> str:
    """Generate recommendations based on findings."""
    recommendations = [
        "1. Implement input validation and sanitization",
        "2. Apply security patches and updates",
        "3. Configure proper access controls",
        "4. Enable security logging and monitoring",
        "5. Conduct security awareness training"
    ]
    return '\n'.join(recommendations)

class SecurityAgentOrchestrator:
    """Orchestrates AI security agents using CrewAI."""
    
    def __init__(self):
        self.ai_config = get_ai_config()
        self.agents = self._create_agents()
        self.tools = self._create_tools()
        
        console.print(Panel.fit(
            "🤖 [bold blue]AI Security Agent System Initialized[/bold blue]\n"
            f"Agents: {len(self.agents)} | Tools: {len(self.tools)}",
            border_style="blue"
        ))
    
    def _create_tools(self) -> List:
        """Create security analysis tools."""
        # Using simple functions instead of BaseTool classes for compatibility
        return []
    
    def _create_agents(self) -> Dict[str, Agent]:
        """Create specialized security agents."""
        agents = {}
        
        # Security Analyst - Main coordinator
        agents['security_analyst'] = Agent(
            role="Senior Security Analyst",
            goal="Coordinate security testing activities and provide strategic analysis",
            backstory="You are an experienced cybersecurity professional with expertise in "
                     "vulnerability assessment, threat analysis, and security architecture. "
                     "You coordinate security testing activities and provide strategic insights.",
            # tools=self.tools,  # Temporarily disabled for compatibility
            llm=self.ai_config.create_llm_for_agent('security_analyst'),
            verbose=True,
            allow_delegation=True
        )
        
        # Vulnerability Scanner - Finds security issues
        agents['vulnerability_scanner'] = Agent(
            role="Vulnerability Assessment Specialist",
            goal="Identify and analyze security vulnerabilities in applications and systems",
            backstory="You are a specialist in vulnerability assessment with deep knowledge of "
                     "OWASP Top 10, CVE databases, and security scanning techniques. "
                     "You excel at finding and categorizing security weaknesses.",
            # tools=self.tools,  # Temporarily disabled for compatibility
            llm=self.ai_config.create_llm_for_agent('vulnerability_scanner'),
            verbose=True
        )

        # Penetration Tester - Exploits vulnerabilities
        agents['penetration_tester'] = Agent(
            role="Ethical Penetration Tester",
            goal="Safely test and validate security vulnerabilities through controlled exploitation",
            backstory="You are an ethical hacker with extensive experience in penetration testing. "
                     "You specialize in safely exploiting vulnerabilities to demonstrate their impact "
                     "while maintaining strict ethical guidelines and only testing authorized systems.",
            # tools=self.tools,  # Temporarily disabled for compatibility
            llm=self.ai_config.create_llm_for_agent('penetration_tester'),
            verbose=True
        )

        # Report Generator - Creates comprehensive reports
        agents['report_generator'] = Agent(
            role="Security Report Specialist",
            goal="Generate comprehensive, actionable security assessment reports",
            backstory="You are a security documentation expert who creates clear, detailed reports "
                     "that communicate technical findings to both technical and executive audiences. "
                     "You excel at risk assessment and providing actionable recommendations.",
            # tools=self.tools,  # Temporarily disabled for compatibility
            llm=self.ai_config.create_llm_for_agent('report_generator'),
            verbose=True
        )
        
        logger.info(f"🤖 Created {len(agents)} AI security agents")
        return agents
    
    def create_security_assessment_crew(self, target: str, scan_results: Dict[str, Any]) -> Crew:
        """Create a crew for comprehensive security assessment."""
        
        # Define tasks for the crew
        tasks = []
        
        # Vulnerability Analysis Task
        vuln_task = Task(
            description=f"""
            Analyze the security scan results for {target} and identify all vulnerabilities.
            Scan results: {json.dumps(scan_results, indent=2)}
            
            Provide a detailed analysis including:
            1. List of identified vulnerabilities
            2. Severity assessment for each vulnerability
            3. Potential impact analysis
            4. Technical details and evidence
            """,
            agent=self.agents['vulnerability_scanner'],
            expected_output="Detailed vulnerability analysis report with severity ratings"
        )
        tasks.append(vuln_task)
        
        # Penetration Testing Task
        pentest_task = Task(
            description=f"""
            Based on the vulnerability analysis, provide safe exploitation guidance for {target}.
            Focus on the most critical vulnerabilities identified.
            
            Provide:
            1. Exploitation techniques for identified vulnerabilities
            2. Proof-of-concept suggestions (safe and ethical)
            3. Risk validation methods
            4. Mitigation bypass techniques to test
            
            Remember: Only suggest techniques for authorized testing!
            """,
            agent=self.agents['penetration_tester'],
            expected_output="Ethical exploitation guide with proof-of-concept suggestions"
        )
        tasks.append(pentest_task)
        
        # Report Generation Task
        report_task = Task(
            description=f"""
            Generate a comprehensive security assessment report for {target}.
            Consolidate findings from vulnerability analysis and penetration testing.
            
            Include:
            1. Executive summary
            2. Detailed findings with evidence
            3. Risk assessment and business impact
            4. Prioritized remediation recommendations
            5. Next steps and follow-up actions
            """,
            agent=self.agents['report_generator'],
            expected_output="Comprehensive security assessment report"
        )
        tasks.append(report_task)
        
        # Create and return the crew
        crew = Crew(
            agents=list(self.agents.values()),
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )
        
        return crew
    
    def run_security_assessment(self, target: str, scan_results: Dict[str, Any]) -> str:
        """Run a complete AI-powered security assessment."""
        console.print(f"🚀 [bold green]Starting AI Security Assessment for: {target}[/bold green]")
        
        try:
            crew = self.create_security_assessment_crew(target, scan_results)
            result = crew.kickoff()
            
            console.print("✅ [bold green]AI Security Assessment Completed![/bold green]")
            return str(result)
            
        except Exception as e:
            error_msg = f"❌ AI Security Assessment failed: {str(e)}"
            logger.error(error_msg)
            console.print(f"[bold red]{error_msg}[/bold red]")
            return error_msg

# Global orchestrator instance
orchestrator = None

def get_security_orchestrator() -> SecurityAgentOrchestrator:
    """Get the global security agent orchestrator."""
    global orchestrator
    if orchestrator is None:
        orchestrator = SecurityAgentOrchestrator()
    return orchestrator

if __name__ == "__main__":
    # Test the agents
    logging.basicConfig(level=logging.INFO)
    
    try:
        orch = SecurityAgentOrchestrator()
        
        # Test with sample scan results
        sample_results = {
            "vulnerabilities": [
                {"severity": "HIGH", "title": "SQL Injection in login form"},
                {"severity": "MEDIUM", "title": "Cross-Site Scripting in search"}
            ],
            "summary": "2 vulnerabilities found in web application"
        }
        
        result = orch.run_security_assessment("http://localhost:8000", sample_results)
        print("\n" + "="*50)
        print("AI SECURITY ASSESSMENT RESULT:")
        print("="*50)
        print(result)
        
    except Exception as e:
        print(f"❌ Failed to test AI agents: {e}")
