# Core Security Testing Framework
crewai>=0.28.0
crewai-tools>=0.1.6

# AI/LLM Integration
openai>=1.0.0
litellm>=1.40.0

# Static Analysis Security Testing (SAST)
semgrep>=1.45.0
bandit[toml]>=1.7.5
safety>=2.3.0
pip-audit>=2.6.0

# Dynamic Application Security Testing (DAST)
python-owasp-zap-v2.4>=0.0.21
requests>=2.31.0
urllib3>=2.0.0

# Dependency Scanning & Vulnerability Management
# Note: Trivy is installed via binary, not pip

# Fuzzing & Exploit Framework
boofuzz>=0.4.1
pwntools>=4.11.0
scapy>=2.5.0
paramiko>=3.3.0

# Web Application Testing
selenium>=4.15.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
httpx>=0.25.0
aiohttp>=3.9.0

# Network & Protocol Testing
nmap>=0.0.1
python-nmap>=0.7.1
netaddr>=0.9.0
dnspython>=2.4.0

# Cryptography & Encoding
cryptography>=41.0.0
pycryptodome>=3.19.0
hashlib-compat>=1.0.1

# Exploit Development & Payloads
impacket>=0.11.0
ldap3>=2.9.0
pysmb>=1.2.9

# Report Generation & Data Processing
jinja2>=3.1.0
markdown>=3.5.0
reportlab>=4.0.0
pandas>=2.1.0
numpy>=1.25.0

# Logging & Monitoring
structlog>=23.2.0
colorama>=0.4.6
rich>=13.7.0

# Configuration & Environment
python-dotenv>=1.0.0
pyyaml>=6.0.1
toml>=0.10.2

# Testing & Quality Assurance
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.9.0
flake8>=6.1.0

# Database & Storage (for vulnerability databases)
sqlite3-utils>=3.35.0
sqlalchemy>=2.0.0

# Async & Concurrency
asyncio-throttle>=1.0.2
aiofiles>=23.2.0

# Docker & Container Security
docker>=6.1.0
python-docker>=0.1.10

# API & Web Framework Testing
fastapi>=0.104.0
flask>=3.0.0
django>=4.2.0

# Machine Learning for Anomaly Detection (optional)
scikit-learn>=1.3.0
tensorflow>=2.14.0

# Additional Security Tools
yara-python>=4.3.1
volatility3>=2.5.0
