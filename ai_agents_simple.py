#!/usr/bin/env python3
"""
security_mvp/ai_agents_simple.py

Simplified AI Security Agents using direct OpenRouter integration
Bypasses CrewAI to avoid LLM interface issues.
"""
import logging
import json
from typing import Dict, Any, List
from ai_config import AIConfigManager

logger = logging.getLogger(__name__)

class SecurityAgent:
    """Individual AI security agent with direct OpenRouter integration."""
    
    def __init__(self, name: str, role: str, goal: str, backstory: str, ai_config: AIConfigManager):
        self.name = name
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.ai_config = ai_config
        
    def analyze(self, task_description: str, context: Dict[str, Any] = None) -> str:
        """Perform analysis using the agent's specialized knowledge."""
        try:
            # Create a comprehensive prompt
            prompt = self._create_prompt(task_description, context)
            
            # Get agent configuration
            config = self.ai_config.get_agent_config(self.name)
            
            # Get response from OpenRouter
            response = self.ai_config.client.chat.completions.create(
                model=config.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=config.max_tokens,
                temperature=config.temperature
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"❌ Agent {self.name} analysis failed: {e}")
            return f"Error: Agent {self.name} failed to complete analysis: {str(e)}"
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        config = self.ai_config.get_agent_config(self.name)
        reasoning_instruction = ""
        if config.reasoning:
            reasoning_instruction = "\n\nIMPORTANT: Think step by step and provide detailed reasoning for your analysis. Explain your thought process clearly."
        
        return f"""You are a {self.role}.

GOAL: {self.goal}

BACKGROUND: {self.backstory}

You are part of a security assessment team. Provide professional, detailed, and actionable analysis.{reasoning_instruction}"""
    
    def _create_prompt(self, task_description: str, context: Dict[str, Any] = None) -> str:
        """Create a comprehensive prompt for the task."""
        prompt = f"TASK: {task_description}\n\n"
        
        if context:
            prompt += "CONTEXT:\n"
            for key, value in context.items():
                if isinstance(value, (dict, list)):
                    prompt += f"{key}: {json.dumps(value, indent=2)}\n"
                else:
                    prompt += f"{key}: {value}\n"
            prompt += "\n"
        
        prompt += "Please provide your analysis:"
        return prompt

class SimpleSecurityOrchestrator:
    """Orchestrates AI security agents using direct OpenRouter integration."""
    
    def __init__(self):
        self.ai_config = AIConfigManager()
        self.agents = self._create_agents()
        logger.info(f"🤖 Created {len(self.agents)} AI security agents")
    
    def _create_agents(self) -> Dict[str, SecurityAgent]:
        """Create specialized security agents."""
        agents = {}
        
        # Security Analyst - Main coordinator
        agents['security_analyst'] = SecurityAgent(
            name='security_analyst',
            role="Vulnerability Assessment Specialist",
            goal="Analyze security scan results and identify all vulnerabilities with detailed technical analysis",
            backstory="You are an expert security analyst with 15+ years of experience in vulnerability assessment and penetration testing. You excel at analyzing scan results and providing detailed technical insights.",
            ai_config=self.ai_config
        )
        
        # Vulnerability Scanner - Finds security issues  
        agents['vulnerability_scanner'] = SecurityAgent(
            name='vulnerability_scanner',
            role="Advanced Penetration Tester",
            goal="Perform deep penetration testing and exploit vulnerabilities to demonstrate real-world impact",
            backstory="You are a skilled penetration tester who specializes in exploiting vulnerabilities to show their real-world impact. You think like an attacker and find creative ways to exploit security weaknesses.",
            ai_config=self.ai_config
        )
        
        # Penetration Tester - Exploits vulnerabilities
        agents['penetration_tester'] = SecurityAgent(
            name='penetration_tester',
            role="Security Remediation Expert", 
            goal="Provide detailed remediation strategies and security recommendations",
            backstory="You are a security architect who specializes in fixing vulnerabilities and implementing security best practices. You provide actionable remediation steps and preventive measures.",
            ai_config=self.ai_config
        )
        
        # Report Generator - Creates comprehensive reports
        agents['report_generator'] = SecurityAgent(
            name='report_generator',
            role="Security Report Specialist",
            goal="Generate comprehensive security assessment reports with executive summaries and technical details",
            backstory="You are a security consultant who excels at creating detailed, professional security reports for both technical teams and executives. You present findings clearly and prioritize risks effectively.",
            ai_config=self.ai_config
        )
        
        return agents
    
    def run_security_assessment(self, target_url: str, scan_results: Dict[str, Any]) -> Dict[str, str]:
        """Run a complete security assessment using all agents."""
        print(f"🚀 Starting AI Security Assessment for: {target_url}")
        print("=" * 60)
        
        results = {}
        
        # Step 1: Vulnerability Analysis
        print("\n🔍 Step 1: Vulnerability Analysis")
        print("-" * 40)
        task_1 = f"""Analyze the security scan results for {target_url} and identify all vulnerabilities.

Scan results: {json.dumps(scan_results, indent=2)}

Provide a detailed analysis including:
1. List of identified vulnerabilities
2. Severity assessment for each vulnerability
3. Potential impact analysis
4. Technical details and evidence"""
        
        results['vulnerability_analysis'] = self.agents['security_analyst'].analyze(task_1)
        print("✅ Vulnerability analysis complete")
        
        # Step 2: Penetration Testing Analysis
        print("\n🎯 Step 2: Penetration Testing Analysis")
        print("-" * 40)
        task_2 = f"""Based on the vulnerability analysis, provide penetration testing insights for {target_url}.

Previous analysis: {results['vulnerability_analysis'][:500]}...

Provide:
1. Exploitation strategies for identified vulnerabilities
2. Attack vectors and techniques
3. Proof-of-concept approaches
4. Risk assessment from an attacker's perspective"""
        
        results['penetration_analysis'] = self.agents['vulnerability_scanner'].analyze(task_2)
        print("✅ Penetration testing analysis complete")
        
        # Step 3: Remediation Recommendations
        print("\n🛠️ Step 3: Remediation Recommendations")
        print("-" * 40)
        task_3 = f"""Provide detailed remediation strategies for the vulnerabilities found in {target_url}.

Vulnerability analysis: {results['vulnerability_analysis'][:500]}...
Penetration analysis: {results['penetration_analysis'][:500]}...

Provide:
1. Specific remediation steps for each vulnerability
2. Security best practices to implement
3. Preventive measures for future protection
4. Priority recommendations based on risk level"""
        
        results['remediation_recommendations'] = self.agents['penetration_tester'].analyze(task_3)
        print("✅ Remediation recommendations complete")
        
        # Step 4: Executive Report
        print("\n📊 Step 4: Executive Report Generation")
        print("-" * 40)
        task_4 = f"""Generate a comprehensive security assessment report for {target_url}.

Vulnerability analysis: {results['vulnerability_analysis'][:300]}...
Penetration analysis: {results['penetration_analysis'][:300]}...
Remediation recommendations: {results['remediation_recommendations'][:300]}...

Create a professional report with:
1. Executive summary
2. Risk assessment and prioritization
3. Technical findings summary
4. Recommended action plan
5. Timeline for remediation"""
        
        results['executive_report'] = self.agents['report_generator'].analyze(task_4)
        print("✅ Executive report complete")
        
        print("\n🎉 AI Security Assessment Complete!")
        print("=" * 60)
        
        return results

def main():
    """Test the simplified AI agents."""
    # Test data
    test_scan_results = {
        "vulnerabilities": [
            {
                "severity": "HIGH",
                "title": "SQL Injection in login form"
            },
            {
                "severity": "MEDIUM", 
                "title": "Cross-Site Scripting in search"
            }
        ],
        "summary": "2 vulnerabilities found in web application"
    }
    
    # Create orchestrator and run assessment
    orchestrator = SimpleSecurityOrchestrator()
    results = orchestrator.run_security_assessment("http://localhost:8000", test_scan_results)
    
    # Display results
    print("\n" + "=" * 80)
    print("AI SECURITY ASSESSMENT RESULTS")
    print("=" * 80)
    
    for step, result in results.items():
        print(f"\n{step.upper().replace('_', ' ')}:")
        print("-" * 50)
        print(result[:500] + "..." if len(result) > 500 else result)

if __name__ == "__main__":
    main()
