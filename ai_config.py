#!/usr/bin/env python3
"""
security_mvp/ai_config.py

AI Configuration Manager for Security MVP
Handles OpenRouter integration and model configuration for CrewAI agents.
"""
import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class AgentConfig:
    """Configuration for an individual AI agent."""
    name: str
    model: str
    reasoning: bool
    max_tokens: int = 4000
    temperature: float = 0.7
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if not self.model:
            raise ValueError(f"Model not specified for agent {self.name}")
        if self.max_tokens <= 0:
            raise ValueError(f"Invalid max_tokens for agent {self.name}: {self.max_tokens}")
        if not 0 <= self.temperature <= 2:
            raise ValueError(f"Invalid temperature for agent {self.name}: {self.temperature}")

class AIConfigManager:
    """Manages AI configuration and OpenRouter integration for security agents."""
    
    def __init__(self):
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
        if not self.openrouter_api_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")
        
        # Initialize OpenAI client for OpenRouter
        self.client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.openrouter_api_key,
            default_headers={
                "HTTP-Referer": "https://github.com/PapaBear1981/ThePenetrator",
                "X-Title": "Security MVP - AI Security Testing Framework"
            }
        )
        
        # Default configuration
        self.default_model = os.getenv("DEFAULT_MODEL", "openrouter/cypher-alpha:free")
        self.default_reasoning = os.getenv("DEFAULT_REASONING", "false").lower() == "true"
        self.default_max_tokens = int(os.getenv("MAX_TOKENS", "4000"))
        self.default_temperature = float(os.getenv("TEMPERATURE", "0.7"))
        
        # Load agent configurations
        self.agents = self._load_agent_configs()
        
        logger.info(f"🤖 AI Config Manager initialized with {len(self.agents)} agents")
        logger.info(f"🔑 Using OpenRouter API with default model: {self.default_model}")
    
    def _load_agent_configs(self) -> Dict[str, AgentConfig]:
        """Load configuration for all security agents."""
        agents = {}
        
        # Define all security agents
        agent_definitions = [
            "SECURITY_ANALYST",
            "VULNERABILITY_SCANNER", 
            "PENETRATION_TESTER",
            "REPORT_GENERATOR",
            "FUZZER",
            "DAST_AGENT"
        ]
        
        for agent_name in agent_definitions:
            model_key = f"{agent_name}_MODEL"
            reasoning_key = f"{agent_name}_REASONING"
            
            model = os.getenv(model_key, self.default_model)
            reasoning = os.getenv(reasoning_key, str(self.default_reasoning)).lower() == "true"
            
            agents[agent_name.lower()] = AgentConfig(
                name=agent_name.lower(),
                model=model,
                reasoning=reasoning,
                max_tokens=self.default_max_tokens,
                temperature=self.default_temperature
            )
            
            logger.debug(f"📋 Loaded config for {agent_name}: {model} (reasoning: {reasoning})")
        
        return agents
    
    def get_agent_config(self, agent_name: str) -> AgentConfig:
        """Get configuration for a specific agent."""
        agent_name = agent_name.lower()
        if agent_name not in self.agents:
            logger.warning(f"⚠️ Agent {agent_name} not found, using default config")
            return AgentConfig(
                name=agent_name,
                model=self.default_model,
                reasoning=self.default_reasoning,
                max_tokens=self.default_max_tokens,
                temperature=self.default_temperature
            )
        return self.agents[agent_name]
    
    def create_llm_for_agent(self, agent_name: str) -> Any:
        """Create an LLM instance for a specific agent using CrewAI's LLM class."""
        config = self.get_agent_config(agent_name)

        # Use CrewAI's built-in LLM class with OpenRouter configuration
        from crewai import LLM

        return LLM(
            model=config.model,
            api_key=self.openrouter_api_key,
            base_url="https://openrouter.ai/api/v1",
            max_tokens=config.max_tokens,
            temperature=config.temperature,
            extra_headers={
                "HTTP-Referer": "https://github.com/PapaBear1981/ThePenetrator",
                "X-Title": "Security MVP - AI Security Testing Framework"
            }
        )
    
    def test_connection(self) -> bool:
        """Test connection to OpenRouter API."""
        try:
            response = self.client.chat.completions.create(
                model=self.default_model,
                messages=[{"role": "user", "content": "Hello, this is a connection test."}],
                max_tokens=10
            )
            logger.info("✅ OpenRouter connection test successful")
            return True
        except Exception as e:
            logger.error(f"❌ OpenRouter connection test failed: {e}")
            return False
    
    def get_available_models(self) -> list:
        """Get list of available models (for future enhancement)."""
        # This would require calling OpenRouter's models endpoint
        # For now, return the configured models
        return list(set(config.model for config in self.agents.values()))
    
    def get_agent_summary(self) -> Dict[str, Dict[str, Any]]:
        """Get summary of all agent configurations."""
        summary = {}
        for name, config in self.agents.items():
            summary[name] = {
                "model": config.model,
                "reasoning": config.reasoning,
                "max_tokens": config.max_tokens,
                "temperature": config.temperature
            }
        return summary

class OpenRouterLLM:
    """OpenRouter LLM wrapper for CrewAI compatibility using BaseLLM."""

    def __init__(self, client: OpenAI, model: str, max_tokens: int = 4000,
                 temperature: float = 0.7, reasoning: bool = False):
        # IMPORTANT: Call super().__init__() with required parameters
        from crewai import BaseLLM

        # Create a proper BaseLLM subclass
        class OpenRouterBaseLLM(BaseLLM):
            def __init__(self, client, model, max_tokens, temperature, reasoning):
                super().__init__(model=model, temperature=temperature)
                self.client = client
                self.max_tokens = max_tokens
                self.reasoning = reasoning
                logger.debug(f"🧠 Created OpenRouter BaseLLM: {model} (reasoning: {reasoning})")

            def call(self, messages, tools=None, callbacks=None, available_functions=None):
                """Call the LLM with the given messages."""
                try:
                    # Convert string to message format if needed
                    if isinstance(messages, str):
                        messages = [{"role": "user", "content": messages}]

                    # Add reasoning instruction if enabled
                    if self.reasoning:
                        system_msg = {
                            "role": "system",
                            "content": "Think step by step and provide detailed reasoning for your analysis. Explain your thought process clearly."
                        }
                        if not messages or messages[0]["role"] != "system":
                            messages.insert(0, system_msg)

                    # Make API call
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                        max_tokens=self.max_tokens,
                        temperature=self.temperature
                    )

                    return response.choices[0].message.content

                except Exception as e:
                    logger.error(f"❌ OpenRouter LLM call failed: {e}")
                    return f"Error: Failed to get response from {self.model}: {str(e)}"

            def supports_function_calling(self) -> bool:
                """Override if your LLM supports function calling."""
                return False  # OpenRouter cypher-alpha doesn't support function calling

            def get_context_window_size(self) -> int:
                """Return the context window size of your LLM."""
                return 8192  # Adjust based on model's actual context window

        # Create and store the BaseLLM instance
        self.llm = OpenRouterBaseLLM(client, model, max_tokens, temperature, reasoning)

    def __getattr__(self, name):
        """Delegate all attribute access to the BaseLLM instance."""
        return getattr(self.llm, name)

# Global instance
ai_config = None

def get_ai_config() -> AIConfigManager:
    """Get the global AI configuration manager instance."""
    global ai_config
    if ai_config is None:
        ai_config = AIConfigManager()
    return ai_config

def initialize_ai_config() -> AIConfigManager:
    """Initialize and return the AI configuration manager."""
    global ai_config
    ai_config = AIConfigManager()
    return ai_config

if __name__ == "__main__":
    # Test the configuration
    logging.basicConfig(level=logging.DEBUG)
    
    try:
        config_manager = initialize_ai_config()
        
        print("🤖 AI Configuration Summary:")
        summary = config_manager.get_agent_summary()
        for agent, config in summary.items():
            print(f"  {agent}: {config['model']} (reasoning: {config['reasoning']})")
        
        print("\n🔍 Testing OpenRouter connection...")
        if config_manager.test_connection():
            print("✅ Connection successful!")
        else:
            print("❌ Connection failed!")
            
    except Exception as e:
        print(f"❌ Failed to initialize AI config: {e}")
