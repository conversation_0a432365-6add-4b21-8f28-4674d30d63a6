# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Security testing results and temporary files
results_*/
*.json
*.log
*.db
*.sqlite
*.sqlite3
test.db
fuzz_results_*.json
scan_results_*.json
security_results_*.json

# Docker
.dockerignore

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Sensitive configuration (keep template)
config_local.yml
secrets.yml
.secrets

# Test artifacts
files/
test_files/
vulnerable_test_data/

# Reports and outputs
reports/
output/
scans/
exploits/

# Backup files
*.bak
*.backup
*.old

# Compiled binaries
*.exe
*.dll
*.so
*.dylib

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# Node modules (if any JS tools are added)
node_modules/

# Jupyter Notebooks checkpoints
.ipynb_checkpoints/

# Environment variables
.env.local
.env.development
.env.test
.env.production

# MacOS
.AppleDouble
.LSOverride

# Windows
desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*
