{"ai_assessment_completed": true, "ai_assessment_file": "results_20250706_100409\\ai_security_assessment.md", "target": "http://localhost:8000", "timestamp": "2025-07-06T10:05:55.625736", "scan_results_analyzed": 0, "ai_config_summary": {"security_analyst": {"model": "qwen/qwen-2.5-coder-32b-instruct:free", "reasoning": false, "max_tokens": 4000, "temperature": 0.7}, "vulnerability_scanner": {"model": "qwen/qwen-2.5-coder-32b-instruct:free", "reasoning": true, "max_tokens": 4000, "temperature": 0.7}, "penetration_tester": {"model": "qwen/qwen-2.5-coder-32b-instruct:free", "reasoning": true, "max_tokens": 4000, "temperature": 0.7}, "report_generator": {"model": "qwen/qwen-2.5-coder-32b-instruct:free", "reasoning": false, "max_tokens": 4000, "temperature": 0.7}, "fuzzer": {"model": "qwen/qwen-2.5-coder-32b-instruct:free", "reasoning": true, "max_tokens": 4000, "temperature": 0.7}, "dast_agent": {"model": "qwen/qwen-2.5-coder-32b-instruct:free", "reasoning": true, "max_tokens": 4000, "temperature": 0.7}}}